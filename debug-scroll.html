<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Horizontal Scroll</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .debug-container {
            width: 400px;
            height: 300px;
            border: 2px solid #007acc;
            margin: 20px 0;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }
        
        .debug-container::-webkit-scrollbar {
            height: 8px;
        }
        
        .debug-container::-webkit-scrollbar-thumb {
            background: #007acc;
            border-radius: 4px;
        }
        
        .wide-content {
            width: 800px;
            height: 100%;
            background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 50%, #d0d0d0 100%);
            display: flex;
            align-items: center;
            justify-content: space-around;
            white-space: nowrap;
        }
        
        .tree-node {
            background: #fff;
            border: 1px solid #ccc;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
        }
        
        .tree-node:hover {
            background: #f5f5f5;
        }
        
        .status {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .instructions {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>调试水平滚动问题</h1>
    
    <div class="instructions">
        <h3>测试步骤：</h3>
        <ol>
            <li>将鼠标放在蓝色容器内的任何位置</li>
            <li>使用触摸板左右滑动</li>
            <li>观察下方的调试信息</li>
            <li>检查容器是否水平滚动</li>
        </ol>
    </div>
    
    <div class="status" id="debugInfo">
        等待滚动事件...
    </div>
    
    <div class="debug-container" id="scrollContainer">
        <div class="wide-content">
            <div class="tree-node">节点 1 - 很长的文件名</div>
            <div class="tree-node">节点 2 - 另一个很长的文件名</div>
            <div class="tree-node">节点 3 - 第三个很长的文件名</div>
            <div class="tree-node">节点 4 - 最后一个很长的文件名</div>
        </div>
    </div>
    
    <div class="status">
        <strong>当前滚动位置：</strong> <span id="scrollPosition">0px</span><br>
        <strong>容器宽度：</strong> <span id="containerWidth">0px</span><br>
        <strong>内容宽度：</strong> <span id="contentWidth">0px</span>
    </div>

    <script>
        const container = document.getElementById('scrollContainer');
        const debugInfo = document.getElementById('debugInfo');
        const scrollPosition = document.getElementById('scrollPosition');
        const containerWidth = document.getElementById('containerWidth');
        const contentWidth = document.getElementById('contentWidth');
        
        // 更新尺寸信息
        function updateSizeInfo() {
            const content = container.querySelector('.wide-content');
            containerWidth.textContent = container.clientWidth + 'px';
            contentWidth.textContent = content.scrollWidth + 'px';
        }
        
        // 更新滚动位置
        function updateScrollPosition() {
            scrollPosition.textContent = container.scrollLeft + 'px';
        }
        
        // 处理滚动事件
        function handleWheel(e) {
            const deltaX = e.deltaX;
            const deltaY = e.deltaY;
            
            debugInfo.innerHTML = `
                滚动事件检测到:<br>
                deltaX: ${deltaX.toFixed(2)}<br>
                deltaY: ${deltaY.toFixed(2)}<br>
                目标元素: ${e.target.tagName}.${e.target.className}<br>
                时间: ${new Date().toLocaleTimeString()}
            `;
            
            // 如果主要是水平滚动
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 0) {
                e.preventDefault();
                container.scrollLeft += deltaX;
                debugInfo.innerHTML += '<br><strong style="color: green;">✓ 水平滚动已处理</strong>';
            } else if (Math.abs(deltaY) > 0) {
                debugInfo.innerHTML += '<br><strong style="color: orange;">⚠ 垂直滚动，未处理</strong>';
            }
            
            updateScrollPosition();
        }
        
        // 监听滚动事件
        container.addEventListener('wheel', handleWheel, { passive: false });
        
        // 监听滚动位置变化
        container.addEventListener('scroll', updateScrollPosition);
        
        // 初始化
        updateSizeInfo();
        updateScrollPosition();
        
        // 窗口大小变化时更新信息
        window.addEventListener('resize', updateSizeInfo);
    </script>
</body>
</html>
