import { DownOutlined, PlayCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon';
import {
  ArexMenuFC,
  CategoryKey,
  clearLocalStorage,
  EmptyWrapper,
  FlexCenterWrapper,
  getLocalStorage,
  Operator,
  RequestMethodEnum,
  SearchDataType,
  StructuredFilter,
  styled,
  TooltipButton,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest, useSize } from 'ahooks';
import { App, Tag, Tree } from 'antd';
import type { DataNode, DirectoryTreeProps } from 'antd/lib/tree';
import { cloneDeep } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { CollectionNodeType, EMAIL_KEY, PanesType, WORKSPACE_STORAGE_KEY } from '@/constant';
import { useNavPane } from '@/hooks';
import { FileSystemService, ReportService } from '@/services';
import { CollectionType, queryDefaultCaseWorkspace } from '@/services/FileSystemService';
import { useCollections, useMenusPanes, useWorkspaces } from '@/store';
import { negate } from '@/utils';
import treeFilter from '@/utils/treeFilter';
import CollectionNodeTitle from '../Collection/CollectionNodeTitle';
// import IconArchive from '~icons/lucide/archive';

const CollectionWrapper = styled.div`
  width: 100%;
  overflow: hidden;

  .collection-content-wrapper {
    padding: 8px 8px;
  }

  .ant-spin-nested-loading,
  .ant-spin {
    height: 100%;
    max-height: 100% !important;
  }

  .collection-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .ant-tree {
    background-color: transparent;
  }

  .ant-tree-node-selected .content {
    color: ${(props) => props.theme.colorText};
  }

  .ant-tree-node-content-wrapper {
    overflow-y: visible; //解决拖拽图标被隐藏
    overflow-x: visible; //允许内容溢出
    white-space: nowrap; //溢出不换行
  }
`;

const CollectionTreeWrapper = styled.div`
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;

  /* 自定义滚动条样式，将滚动条放在底部 */
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #88888844;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #888888aa;
  }

  /* 创建固定的操作按钮区域 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 6px; /* 为滚动条留出空间 */
    width: 40px;
    background: linear-gradient(to left, ${(props) => props.theme.colorBgContainer} 70%, transparent);
    pointer-events: none;
    z-index: 5;
  }

  /* 隐藏原始的操作按钮，我们将用 JavaScript 动态创建固定位置的按钮 */
  .ant-tree-node-content-wrapper .right {
    display: none;
  }
`;

const CollectionTree = styled(Tree<CollectionType>)`
  min-width: max-content; // 恢复原有的宽度设置，确保可以水平滚动

  .ant-tree-node-selected {
    background-color: ${(props) => props.theme.colorPrimaryBgHover} !important;
  }
`;

export type CollectionTreeType = CollectionType & DataNode;

const AutoCase: ArexMenuFC = (props) => {
  const { t } = useTranslation(['components']);
  // const { message } = App.useApp();
  const navPane = useNavPane();
  const size = useSize(() => document.getElementById('arex-menu-wrapper'));

  // const userName = getLocalStorage<string>(EMAIL_KEY) as string;

  const { setActiveWorkspaceId } = useWorkspaces();

  const { activePane, reset: resetPane } = useMenusPanes();
  const { reset, loading, collectionsTreeData, collectionsFlatData, getCollections } = useCollections();

  const [searchValue, setSearchValue] = useState<SearchDataType>();
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  // requestId structure: workspaceId-nodeTypeStr-id
  const selectedKeys = useMemo(() => {
    const id = props.value?.split('-')?.[2];
    return id ? [id] : undefined;
  }, [props.value]);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]); // TODO 初始化展开的节点

  // const activeWorkspaceId = '682c47f8bd26001c54f4f901';
  // console.log('bj',activePane,'682c47f8bd26001c54f4f901')

  useEffect(() => {
    if (location.pathname.startsWith('/autoCase')) {
      getCollections('682c47f8bd26001c54f4f901');
    }
  }, [location.pathname]);

    // auto expand by active pane id
    useEffect(() => {
      activePane &&
        activePane.type === PanesType.AUTOCASE &&
        setExpandedKeys((expandedKeys) => Array.from(new Set([...expandedKeys, activePane.id])));
    }, [activePane]);

  // 处理触摸板水平滚动事件
  const handleWrapperWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    // 只处理水平滚动
    if (Math.abs(e.deltaX) > 0) {
      // 手动处理滚动，不需要阻止默认行为
      e.currentTarget.scrollLeft += e.deltaX;
    }
  }, []);

  // 处理固定操作按钮
  useEffect(() => {
    const container = document.querySelector('#collectTree [style*="overflow-x: auto"]') as HTMLElement;
    if (!container) return;

    let currentHoveredNode: HTMLElement | null = null;
    let fixedButton: HTMLElement | null = null;

    const createFixedButton = (originalButton: HTMLElement) => {
      // 克隆原始按钮
      const clonedButton = originalButton.cloneNode(true) as HTMLElement;

      // 设置固定位置样式
      clonedButton.style.position = 'absolute';
      clonedButton.style.right = '8px';
      clonedButton.style.top = '50%';
      clonedButton.style.transform = 'translateY(-50%)';
      clonedButton.style.zIndex = '1000';
      clonedButton.style.opacity = '1';
      clonedButton.style.background = getComputedStyle(document.documentElement).getPropertyValue('--ant-color-bg-container') || '#ffffff';
      clonedButton.style.display = 'block';

      // 复制事件处理器
      const originalButtonElement = originalButton.querySelector('button');
      const clonedButtonElement = clonedButton.querySelector('button');
      if (originalButtonElement && clonedButtonElement) {
        clonedButtonElement.onclick = originalButtonElement.onclick;
        clonedButtonElement.className = originalButtonElement.className;
      }

      return clonedButton;
    };

    const showFixedButton = (nodeElement: HTMLElement) => {
      if (fixedButton) {
        fixedButton.remove();
        fixedButton = null;
      }

      const originalButton = nodeElement.querySelector('.right') as HTMLElement;
      if (!originalButton) return;

      fixedButton = createFixedButton(originalButton);
      nodeElement.style.position = 'relative';
      nodeElement.appendChild(fixedButton);
    };

    const hideFixedButton = () => {
      if (fixedButton) {
        fixedButton.remove();
        fixedButton = null;
      }
      currentHoveredNode = null;
    };

    const handleMouseEnter = (e: Event) => {
      const target = e.target as HTMLElement;
      const nodeElement = target.closest('.ant-tree-treenode') as HTMLElement;

      if (nodeElement && nodeElement !== currentHoveredNode) {
        currentHoveredNode = nodeElement;
        showFixedButton(nodeElement);
      }
    };

    const handleMouseLeave = (e: Event) => {
      const target = e.target as HTMLElement;
      const nodeElement = target.closest('.ant-tree-treenode') as HTMLElement;

      if (nodeElement === currentHoveredNode) {
        setTimeout(() => {
          if (currentHoveredNode === nodeElement) {
            hideFixedButton();
          }
        }, 100);
      }
    };

    container.addEventListener('mouseenter', handleMouseEnter, true);
    container.addEventListener('mouseleave', handleMouseLeave, true);

    return () => {
      container.removeEventListener('mouseenter', handleMouseEnter, true);
      container.removeEventListener('mouseleave', handleMouseLeave, true);
      hideFixedButton();
    };
  }, []);


  useEffect(() => {

    // 组件挂载时清空 activeWorkspaceId
    clearLocalStorage(WORKSPACE_STORAGE_KEY);
    setActiveWorkspaceId("")

    return () => {
      // 组件卸载时再次清空
      clearLocalStorage(WORKSPACE_STORAGE_KEY);
      setActiveWorkspaceId("")
    };
  }, [setActiveWorkspaceId]);

  const { data: labelData = [] } = useRequest(
    () => ReportService.queryLabels({ workspaceId: '682c47f8bd26001c54f4f901' }),
    {
      refreshDeps: ['682c47f8bd26001c54f4f901'],
    },
  );

  const filterTreeData = useMemo(
    // filter nodeType === 3 是为了隐藏第一层级只显示文件夹类型（由于新增请求时会新增一个临时的request到树形目录的第一层）
    // TODO filter 逻辑待优化
    () =>
      searchValue?.keyword
        ? treeFilter(searchValue.keyword, cloneDeep(collectionsTreeData), 'nodeName')
        : collectionsTreeData,
    [collectionsTreeData, searchValue?.keyword],
  );

  const dataList: { key: string; title: string; labelIds: string | null }[] = useMemo(
    () =>
      Array.from(collectionsFlatData).map(([key, value]) => ({
        key,
        title: value.nodeName,
        labelIds: value.labelIds,
      })),
    [collectionsFlatData],
  );

  const options = useMemo(
    () => [
      {
        category: CategoryKey.Label,
        operator: [Operator.EQ, Operator.NE],
        value: labelData.map((label) => ({
          label: <Tag color={label.color}>{label.labelName}</Tag>,
          key: label.id,
        })),
      },
    ],
    [labelData],
  );

  const handleSelect: DirectoryTreeProps<CollectionTreeType>['onSelect'] = (keys, info) => {
    if (info.node.nodeType !== CollectionNodeType.folder) {
      const icon =
        info.node.nodeType === CollectionNodeType.interface
          ? info.node.method || undefined
          : info.node.nodeType === CollectionNodeType.case
          ? info.node.caseSourceType === 1
            ? 'arex'
            : 'case'
          : undefined;

      navPane({
        type: PanesType.AUTOCASE,
        id: `682c47f8bd26001c54f4f901-${info.node.nodeType}-${info.node.infoId}`,
        name: info.node.nodeName,
        icon,
      });
    }
  };

  const handleChange = (value: SearchDataType) => {
    const { keyword, structuredValue = [] } = value;
    let newExpandedKeys;
    if (!structuredValue?.length && !keyword) {
      // newExpandedKeys = dataList.map((item) => item.title);
    } else {
      newExpandedKeys = dataList
        .map((item) => {
          const lowerCaseKeyword = keyword?.toLowerCase() || '';
          const keywordFiltered =
            !keyword ||
            (lowerCaseKeyword &&
              (item.title.toLowerCase().includes(lowerCaseKeyword) ||
                item.key.toLowerCase().includes(lowerCaseKeyword)));
          let structuredFiltered = true;

          for (let i = 0; i < structuredValue.length; i++) {
            const structured = structuredValue[i];

            if (structured.category === CategoryKey.Label) {
              // search for labelIds
              const include = negate(
                item.labelIds?.includes(structured.value as string),
                structured.operator === Operator.NE,
              );

              if (!include) {
                structuredFiltered = false;
                break;
              }
            }
          }

          return keywordFiltered && structuredFiltered
            ? collectionsFlatData.get(item.key)?.pid
            : null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as string[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys as string[]);
    setAutoExpandParent(false);
  };

  return (
    <CollectionWrapper id='collectTree'>
      <div className='collection-content-wrapper'>
        <EmptyWrapper
          empty={!loading && !collectionsTreeData.length}
          loading={loading}
          // description={
          //   <Button type='primary' onClick={createCollection}>
          //     {t('collection.create_new')}
          //   </Button>
          // }
        >
          {/* Tree模块上方的几个按钮 */}
          <StructuredFilter
            size='small'
            className={'collection-header-search'}
            showSearchButton={false}
            prefix={
              <div
                style={{
                  height: '27px',
                  marginRight: '8px',
                  display: 'flex',
                  flexWrap: 'wrap',
                  alignContent: 'center',
                }}
              >
                <TooltipButton
                  icon={<PlayCircleOutlined />}
                  title={t('collection.batch_run')}
                  onClick={() => {
                    navPane({
                      type: PanesType.BATCH_RUN,
                      id: '682c47f8bd26001c54f4f901',
                    });
                  }}
                />
              </div>
            }
            labelDataSource={labelData.map((item) => ({
              id: item.id,
              name: item.labelName,
              color: item.color,
            }))}
            options={options}
            placeholder={'Search for Name or Id'}
            onChange={handleChange}
          />
          <CollectionTreeWrapper onWheel={handleWrapperWheel}>
            <CollectionTree
              showLine
              blockNode
              height={size?.height && size.height - 100}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              switcherIcon={<DownOutlined />}
              treeData={filterTreeData}
              fieldNames={{ title: 'nodeName', key: 'infoId', children: 'children' }}
              // onDrop={onDrop}
              onExpand={onExpand}
              onSelect={handleSelect}
              draggable={{ icon: false }}
              titleRender={(data) => (
                <CollectionNodeTitle
                  data={data}
                  keyword={searchValue?.keyword}
                  menuSelect={"autoCase"}
                  // onAddNode={handleAddNode}
                />
              )}
            />
          </CollectionTreeWrapper>
        </EmptyWrapper>
      </div>
    </CollectionWrapper>
  );
};

export default AutoCase;
