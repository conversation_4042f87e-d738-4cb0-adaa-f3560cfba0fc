import { DownOutlined, PlayCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon';
import {
  ArexMenuFC,
  CategoryKey,
  clearLocalStorage,
  EmptyWrapper,
  FlexCenterWrapper,
  getLocalStorage,
  Operator,
  RequestMethodEnum,
  SearchDataType,
  StructuredFilter,
  styled,
  TooltipButton,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest, useSize } from 'ahooks';
import { App, Tag, Tree } from 'antd';
import type { DataNode, DirectoryTreeProps } from 'antd/lib/tree';
import { cloneDeep } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { CollectionNodeType, EMAIL_KEY, PanesType, WORKSPACE_STORAGE_KEY } from '@/constant';
import { useNavPane } from '@/hooks';
import { FileSystemService, ReportService } from '@/services';
import { CollectionType, queryDefaultCaseWorkspace } from '@/services/FileSystemService';
import { useCollections, useMenusPanes, useWorkspaces } from '@/store';
import { negate } from '@/utils';
import treeFilter from '@/utils/treeFilter';
import CollectionNodeTitle from '../Collection/CollectionNodeTitle';
// import IconArchive from '~icons/lucide/archive';

const CollectionWrapper = styled.div`
  width: 100%;
  overflow: hidden;

  .collection-content-wrapper {
    padding: 8px 8px;
  }

  .ant-spin-nested-loading,
  .ant-spin {
    height: 100%;
    max-height: 100% !important;
  }

  .collection-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .ant-tree {
    background-color: transparent;
  }

  .ant-tree-node-selected .content {
    color: ${(props) => props.theme.colorText};
  }

  .ant-tree-node-content-wrapper {
    overflow-y: visible; //解决拖拽图标被隐藏
    overflow-x: visible; //允许内容溢出
    white-space: nowrap; //溢出不换行
  }
`;

const CollectionTreeWrapper = styled.div`
  overflow-x: auto;
  overflow-y: hidden;

  /* 自定义滚动条样式，将滚动条放在底部 */
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #88888844;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #888888aa;
  }

  /* 确保所有子元素都能传递滚动事件 */
  * {
    pointer-events: auto;
  }

  /* 确保树节点区域也能响应水平滚动 */
  .ant-tree {
    overflow-x: visible;
    overflow-y: visible;
  }

  .ant-tree-list {
    overflow-x: visible;
    overflow-y: visible;
  }

  .ant-tree-treenode {
    overflow-x: visible;
    overflow-y: visible;
  }
`;

const CollectionTree = styled(Tree<CollectionType>)`
  min-width: max-content; // 确保树形结构不会被压缩，根据内容自动调整宽度

  .ant-tree-node-selected {
    background-color: ${(props) => props.theme.colorPrimaryBgHover} !important;
  }
`;

export type CollectionTreeType = CollectionType & DataNode;

const AutoCase: ArexMenuFC = (props) => {
  const { t } = useTranslation(['components']);
  // const { message } = App.useApp();
  const navPane = useNavPane();
  const size = useSize(() => document.getElementById('arex-menu-wrapper'));

  // const userName = getLocalStorage<string>(EMAIL_KEY) as string;

  const { setActiveWorkspaceId } = useWorkspaces();

  const { activePane, reset: resetPane } = useMenusPanes();
  const { reset, loading, collectionsTreeData, collectionsFlatData, getCollections } = useCollections();

  const [searchValue, setSearchValue] = useState<SearchDataType>();
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  // requestId structure: workspaceId-nodeTypeStr-id
  const selectedKeys = useMemo(() => {
    const id = props.value?.split('-')?.[2];
    return id ? [id] : undefined;
  }, [props.value]);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]); // TODO 初始化展开的节点

  // const activeWorkspaceId = '682c47f8bd26001c54f4f901';
  // console.log('bj',activePane,'682c47f8bd26001c54f4f901')

  useEffect(() => {
    if (location.pathname.startsWith('/autoCase')) {
      getCollections('682c47f8bd26001c54f4f901');
    }
  }, [location.pathname]);

    // auto expand by active pane id
    useEffect(() => {
      activePane &&
        activePane.type === PanesType.AUTOCASE &&
        setExpandedKeys((expandedKeys) => Array.from(new Set([...expandedKeys, activePane.id])));
    }, [activePane]);

  // 处理触摸板水平滚动事件
  const handleWrapperWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    // 只处理水平滚动
    if (Math.abs(e.deltaX) > 0) {
      // 手动处理滚动，不需要阻止默认行为
      e.currentTarget.scrollLeft += e.deltaX;
    }
  }, []);


  useEffect(() => {

    // 组件挂载时清空 activeWorkspaceId
    clearLocalStorage(WORKSPACE_STORAGE_KEY);
    setActiveWorkspaceId("")

    return () => {
      // 组件卸载时再次清空
      clearLocalStorage(WORKSPACE_STORAGE_KEY);
      setActiveWorkspaceId("")
    };
  }, [setActiveWorkspaceId]);

  const { data: labelData = [] } = useRequest(
    () => ReportService.queryLabels({ workspaceId: '682c47f8bd26001c54f4f901' }),
    {
      refreshDeps: ['682c47f8bd26001c54f4f901'],
    },
  );

  const filterTreeData = useMemo(
    // filter nodeType === 3 是为了隐藏第一层级只显示文件夹类型（由于新增请求时会新增一个临时的request到树形目录的第一层）
    // TODO filter 逻辑待优化
    () =>
      searchValue?.keyword
        ? treeFilter(searchValue.keyword, cloneDeep(collectionsTreeData), 'nodeName')
        : collectionsTreeData,
    [collectionsTreeData, searchValue?.keyword],
  );

  const dataList: { key: string; title: string; labelIds: string | null }[] = useMemo(
    () =>
      Array.from(collectionsFlatData).map(([key, value]) => ({
        key,
        title: value.nodeName,
        labelIds: value.labelIds,
      })),
    [collectionsFlatData],
  );

  const options = useMemo(
    () => [
      {
        category: CategoryKey.Label,
        operator: [Operator.EQ, Operator.NE],
        value: labelData.map((label) => ({
          label: <Tag color={label.color}>{label.labelName}</Tag>,
          key: label.id,
        })),
      },
    ],
    [labelData],
  );

  const handleSelect: DirectoryTreeProps<CollectionTreeType>['onSelect'] = (keys, info) => {
    if (info.node.nodeType !== CollectionNodeType.folder) {
      const icon =
        info.node.nodeType === CollectionNodeType.interface
          ? info.node.method || undefined
          : info.node.nodeType === CollectionNodeType.case
          ? info.node.caseSourceType === 1
            ? 'arex'
            : 'case'
          : undefined;

      navPane({
        type: PanesType.AUTOCASE,
        id: `682c47f8bd26001c54f4f901-${info.node.nodeType}-${info.node.infoId}`,
        name: info.node.nodeName,
        icon,
      });
    }
  };

  const handleChange = (value: SearchDataType) => {
    const { keyword, structuredValue = [] } = value;
    let newExpandedKeys;
    if (!structuredValue?.length && !keyword) {
      // newExpandedKeys = dataList.map((item) => item.title);
    } else {
      newExpandedKeys = dataList
        .map((item) => {
          const lowerCaseKeyword = keyword?.toLowerCase() || '';
          const keywordFiltered =
            !keyword ||
            (lowerCaseKeyword &&
              (item.title.toLowerCase().includes(lowerCaseKeyword) ||
                item.key.toLowerCase().includes(lowerCaseKeyword)));
          let structuredFiltered = true;

          for (let i = 0; i < structuredValue.length; i++) {
            const structured = structuredValue[i];

            if (structured.category === CategoryKey.Label) {
              // search for labelIds
              const include = negate(
                item.labelIds?.includes(structured.value as string),
                structured.operator === Operator.NE,
              );

              if (!include) {
                structuredFiltered = false;
                break;
              }
            }
          }

          return keywordFiltered && structuredFiltered
            ? collectionsFlatData.get(item.key)?.pid
            : null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as string[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys as string[]);
    setAutoExpandParent(false);
  };

  return (
    <CollectionWrapper id='collectTree'>
      <div className='collection-content-wrapper'>
        <EmptyWrapper
          empty={!loading && !collectionsTreeData.length}
          loading={loading}
          // description={
          //   <Button type='primary' onClick={createCollection}>
          //     {t('collection.create_new')}
          //   </Button>
          // }
        >
          {/* Tree模块上方的几个按钮 */}
          <StructuredFilter
            size='small'
            className={'collection-header-search'}
            showSearchButton={false}
            prefix={
              <div
                style={{
                  height: '27px',
                  marginRight: '8px',
                  display: 'flex',
                  flexWrap: 'wrap',
                  alignContent: 'center',
                }}
              >
                <TooltipButton
                  icon={<PlayCircleOutlined />}
                  title={t('collection.batch_run')}
                  onClick={() => {
                    navPane({
                      type: PanesType.BATCH_RUN,
                      id: '682c47f8bd26001c54f4f901',
                    });
                  }}
                />
              </div>
            }
            labelDataSource={labelData.map((item) => ({
              id: item.id,
              name: item.labelName,
              color: item.color,
            }))}
            options={options}
            placeholder={'Search for Name or Id'}
            onChange={handleChange}
          />
          <CollectionTreeWrapper onWheel={handleWrapperWheel}>
            <CollectionTree
              showLine
              blockNode
              height={size?.height && size.height - 100}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              switcherIcon={<DownOutlined />}
              treeData={filterTreeData}
              fieldNames={{ title: 'nodeName', key: 'infoId', children: 'children' }}
              // onDrop={onDrop}
              onExpand={onExpand}
              onSelect={handleSelect}
              draggable={{ icon: false }}
              titleRender={(data) => (
                <CollectionNodeTitle
                  data={data}
                  keyword={searchValue?.keyword}
                  menuSelect={"autoCase"}
                  // onAddNode={handleAddNode}
                />
              )}
            />
          </CollectionTreeWrapper>
        </EmptyWrapper>
      </div>
    </CollectionWrapper>
  );
};

export default AutoCase;
