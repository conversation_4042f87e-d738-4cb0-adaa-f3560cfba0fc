import {
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleFilled,
  MoreOutlined,
} from '@ant-design/icons';
import {
  getLocalStorage,
  RequestMethodIcon,
  SmallTextButton,
  styled,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest } from 'ahooks';
import { App, Button, Dropdown, Input, MenuProps, Space } from 'antd';
import React, { FC, ReactNode, useMemo, useState } from 'react';

import { SearchHighLight } from '@/components';
import { CollectionNodeType, EMAIL_KEY, MenusType, PanesType, ProtocalType } from '@/constant';
import { useNavPane } from '@/hooks';
import panes from '@/panes';
import { FileSystemService } from '@/services';
import { CollectionType } from '@/services/FileSystemService';
import { useCollections, useWorkspaces } from '@/store';

import { modifyField } from '../../../../arex-request/src/components/Request/utils/caseGenerator';

const CollectionNodeTitleWrapper = styled.div`
  color: ${(props) => props.theme.colorTextSecondary};
  display: flex;
  position: relative;
  width: 100%;
  max-width: 100%;

  .right {
    opacity: 0;
    transition: opacity ease 0.3s;
    margin-left: 8px;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
  }

  &:hover {
    .right {
      opacity: 1;
    }
  }

  .left {
    flex: 1;
    overflow-x: auto; // 允许内容在节点内部水平滚动
    overflow-y: hidden;
    display: flex;
    align-items: center;
    min-width: 0; // 确保可以收缩
    max-width: calc(100% - 40px); // 为操作按钮预留空间

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none; /* Firefox */

    .content {
      white-space: nowrap; //溢出不换行
      min-width: max-content;
    }
  }

  :hover {
    color: ${(props) => props.theme.colorText};
  }
`;

export type CollectionNodeTitleProps = {
  data: CollectionType;
  keyword?: string;
  onAddNode?: (info: string, nodeType: CollectionNodeType) => void;
  menuSelect?: string;
}

const CollectionNodeTitle: FC<CollectionNodeTitleProps> = (props) => {
  const { modal } = App.useApp();
  const confirm = modal.confirm;
  const { t } = useTranslation(['common', 'components']);
  const navPane = useNavPane();
  const { activeWorkspaceId } = useWorkspaces();
  const { getCollections, getPath } = useCollections();

  const userName = getLocalStorage<string>(EMAIL_KEY) as string;

  const [editMode, setEditMode] = useState(false);
  const [nodeName, setNodeName] = useState(props.data.nodeName);

  const nodePath = useMemo(
    () => getPath(props.data.infoId).map((path) => path.id),
    [getPath, props.data.infoId],
  );

  const workspaceId = props.menuSelect == 'autoCase' ? "682c47f8bd26001c54f4f901" : activeWorkspaceId;

  const { run: addCollectionItem } = useRequest(
    (params: {
      nodeName: string;
      nodeType: CollectionNodeType;
      caseSourceType?: number;
      requestType?: 'HTTP' | 'WMB' | 'SCF' | 'WMB_MOCK' | 'HTTP_COMPARE' | 'SCF_COMPARE';
    }) =>
      FileSystemService.addCollectionItem({
        ...params,
        userName,
        id: workspaceId,
        parentPath: nodePath,
      }),
    {
      manual: true,
      onSuccess: (res, [{ caseSourceType, nodeType }]) => {
        if (res.success) {
          if (caseSourceType === 2) {
            queryInterface({ id: nodePath[nodePath.length - 1] as string }, res.infoId);
          } else {
            getCollections(workspaceId).then(() => props.onAddNode?.(res.infoId, nodeType));
          }
        }
      },
    },
  );

  const { run: duplicateCollectionItem } = useRequest(
    () =>
      FileSystemService.duplicateCollectionItem({
        id: activeWorkspaceId,
        path: nodePath,
        userName,
      }),
    {
      manual: true,
      onSuccess: () => {
        getCollections();
      },
    },
  );

  const { run: rename } = useRequest(
    () =>
      FileSystemService.renameCollectionItem({
        id: workspaceId,
        newName: nodeName,
        path: nodePath,
        userName,
      }),
    {
      manual: true,
      onSuccess(success) {
        setEditMode(false);
        getCollections(workspaceId);
      },
    },
  );

  const { run: removeCollectionItem } = useRequest(
    () =>
      FileSystemService.removeCollectionItem({
        id: workspaceId,
        removeNodePath: nodePath,
        userName,
      }),
    {
      manual: true,
      onSuccess: () => {
        getCollections(workspaceId);
      },
    },
  );

  // 获取当前接口下所有用例的路径
  const getCasesPaths = (): string[][] => {
    const currentNode = props.data;
    if (!currentNode?.children) return [];

    return currentNode.children
      .filter((node) => node.nodeType === CollectionNodeType.case)
      .map((node) => [...nodePath, node.infoId]);
  };

  const { run: removeItems } = useRequest(
    () => {
      const casePaths = getCasesPaths();
      if (casePaths.length === 0) {
        modal.warning({
          content: t('该接口下没有用例可删除'),
        });
        return Promise.reject();
      }
      return FileSystemService.removeItems({
        id: workspaceId,
        removeNodePaths: casePaths,
        userName,
      });
    },
    {
      manual: true,
      onSuccess: () => {
        getCollections(workspaceId);
      },
      onError: (error) => {
        if (error) {
          modal.error({
            title: t('操作失败'),
            content: error.message,
          });
        }
      },
    },
  );

  const { run: saveCase } = useRequest(FileSystemService.saveCase, {
    manual: true,
    onSuccess: (parentInterface, params: any) => {
      const id = params[0]?.id;
      getCollections().then(() => props.onAddNode?.(id, 2));
    },
  });

  const { run: queryInterface } = useRequest(FileSystemService.queryInterface, {
    manual: true,
    onSuccess: (parentInterface, params) => {
      saveCase({
        workspaceId: workspaceId as string,
        ...parentInterface,
        id: params[1],
      });
    },
  });

  const menu: MenuProps = useMemo(
    () => ({
      items: (
        [] as {
          key: string;
          label: ReactNode;
          children?: {
            key: string;
            label: ReactNode;
          }[];
        }[]
      )
        .concat(
          props.data.nodeType === CollectionNodeType.folder
            ? [
                {
                  key: 'batchRun',
                  label: (
                    <a
                      onClick={() => {
                        navPane({
                          type: PanesType.BATCH_RUN,
                          id: `${workspaceId}-${props.data.infoId}`,
                        });
                      }}
                    >
                      {t('collection.batch_run', { ns: 'components' })}
                    </a>
                  ),
                },
                {
                  key: 'mindmapPreview',
                  label: (
                    <a
                      onClick={() => {
                        navPane({
                          type: PanesType.MINDMAP,
                          id: `${activeWorkspaceId}-mindmap-${props.data.infoId}`,
                          data: {
                            workspaceId: activeWorkspaceId,
                            nodeId: props.data.infoId,
                            nodeName: props.data.nodeName,
                          },
                        });
                      }}
                    >
                      {t('collection.mindmap_preview', { ns: 'components' }) || '预览脑图'}
                    </a>
                  ),
                },
                {
                  key: 'addFolder',
                  label: (
                    <a
                      onClick={() =>
                        addCollectionItem({
                          nodeName: 'New Collection',
                          nodeType: CollectionNodeType.folder,
                        })
                      }
                    >
                      {t('collection.add_folder', { ns: 'components' })}
                    </a>
                  ),
                },
                {
                  key: 'addRequest',
                  label: <a>{t('collection.add_request', { ns: 'components' })}</a>,
                  children: [
                    {
                      key: '1',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'HTTP',
                            })
                          }
                        >
                          {t('🌐 HTTP')}
                        </a>
                      ),
                    },
                    {
                      key: '2',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'SCF',
                            })
                          }
                        >
                          {t('♨ SCF')}
                        </a>
                      ),
                    },
                    {
                      key: '3',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'WMB',
                            })
                          }
                        >
                          {t('🧭 WMB')}
                        </a>
                      ),
                    },
                    {
                      key: '4',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'WMB_MOCK',
                            })
                          }
                        >
                          {t('✴️ WMB_MOCK')}
                        </a>
                      ),
                    },
                    {
                      key: '5',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'HTTP_COMPARE',
                            })
                          }
                        >
                          {t('✴️ HTTP_COMPARE')}
                        </a>
                      ),
                    },
                    {
                      key: '6',
                      label: (
                        <a
                          onClick={() =>
                            addCollectionItem({
                              nodeName: 'New Request',
                              nodeType: CollectionNodeType.interface,
                              requestType: 'SCF_COMPARE',
                            })
                          }
                        >
                          {t('✴️ SCF_COMPARE')}
                        </a>
                      ),
                    },
                  ],
                },
              ]
            : [],
        )
        .concat(
          props.data.nodeType === CollectionNodeType.interface && workspaceId != "682c47f8bd26001c54f4f901"
            ? [
                {
                  key: 'addCase',
                  label: (
                    <a
                      onClick={() => {
                        addCollectionItem({
                          nodeName: 'case',
                          nodeType: CollectionNodeType.case,
                          caseSourceType: 2,
                        });
                      }}
                    >
                      {t('collection.add_case', { ns: 'components' })}
                    </a>
                  ),
                },
              ]
            : [],
        )
        .concat([
           ...( props.data.nodeType != CollectionNodeType.folder || workspaceId != "682c47f8bd26001c54f4f901"
            ? [
                {
                   key: 'rename',
                   label: (
                     <a onClick={() => setEditMode(true)}>
                        {t('collection.rename', { ns: 'components' })}
                     </a>
                  ),
                },
              ]
            : []),
          ...( workspaceId !="682c47f8bd26001c54f4f901"
            ? [
                {
                   key: 'duplicate',
                   label: (
                     <a onClick={() => duplicateCollectionItem()}>
                        {t('collection.duplicate', { ns: 'components' })}
                     </a>
                  ),
                },
              ]
            : []),
          {
            key: 'delete',
            label: (
              <a
                style={{ color: 'red' }}
                onClick={() => {
                  confirm({
                    title: t('are_you_sure'),
                    icon: <ExclamationCircleFilled />,
                    okText: 'Yes',
                    okType: 'danger',
                    cancelText: 'No',
                    onOk: removeCollectionItem,
                  });
                }}
              >
                {t('collection.delete', { ns: 'components' })}
              </a>
            ),
          },
          ...(props.data.nodeType === CollectionNodeType.interface
            ? [
                {
                  key: 'batch_delete_case',
                  label: (
                    <a
                      style={{ color: 'red' }}
                      onClick={() => {
                        confirm({
                          content: t('确定要删除该接口下的所有用例？', { ns: 'components' }),
                          icon: <ExclamationCircleFilled />,
                          okText: '确定',
                          okType: 'danger',
                          cancelText: '取消',
                          onOk: removeItems,
                        });
                      }}
                    >
                      {t('collection.batch_delete_case', { ns: 'components' })}
                    </a>
                  ),
                },
              ]
            : []),
        ]),
      onClick(e) {
        e.domEvent.stopPropagation();
      },
    }),
    [],
  );

  const prefix = useMemo(
    () => {
      // console.log(
      //   'AAAA:',
      //   props.data.method,
      //   RequestMethodIcon,
      //   RequestMethodIcon[props.data.method || ''],
      // );

      return props.data.nodeType === CollectionNodeType.interface
        ? ProtocalType.HTTP === props.data.requestType || !props.data.requestType
          ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
          : ProtocalType.SCF === props.data.requestType
          ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
          : React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
        : props.data.nodeType === CollectionNodeType.case
        ? React.createElement(
            RequestMethodIcon[props.data.caseSourceType === 1 ? 'arex' : 'case'] || 'div',
          )
        : '';
    },
    // ? ProtocalType.HTTP
    //   ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
    //   : React.createElement(RequestMethodIcon[''] || 'div')
    // : ProtocalType.SCF
    // ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
    // : React.createElement(RequestMethodIcon['2222'] || 'div'),
    // props.data.nodeType === CollectionNodeType.case
    // ? React.createElement(
    //     RequestMethodIcon[props.data.caseSourceType === 1 ? 'arex' : 'case'] || 'div',
    //   )
    // : null,
    [props.data],
  );

  // const prefix = useMemo(
  //   () => {
  //     return props.data.nodeType === CollectionNodeType.interface
  //       ? ProtocalType.HTTP === props.data.requestType || !props.data.requestType
  //         ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
  //         : ProtocalType.SCF === props.data.requestType
  //           ? React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
  //           : ProtocalType.WMB_MOCK === props.data.requestType
  //             ? React.createElement(RequestMethodIcon[] || 'div') // 处理 Mock 类型
  //             : React.createElement(RequestMethodIcon[props.data.method || ''] || 'div')
  //       : props.data.nodeType === CollectionNodeType.case
  //         ? React.createElement(
  //           RequestMethodIcon[props.data.caseSourceType === 1 ? 'arex' : 'case'] || 'div',
  //         )
  //         : '';
  //   },
  //   [props.data],
  // );

  return (
    <CollectionNodeTitleWrapper>
      <div className={'left'}>
        {prefix}
        <div className={'content'}>
          {editMode ? (
            <Space style={{ display: 'flex' }}>
              <Input
                value={nodeName}
                onPressEnter={rename}
                onChange={(e) => setNodeName(e.currentTarget.value)}
                style={{ padding: '0 4px' }}
              />
              <SmallTextButton
                icon={<CloseOutlined />}
                onClick={() => {
                  setEditMode(false);
                  setNodeName(props.data.nodeName);
                }}
              />
              <SmallTextButton icon={<CheckOutlined />} onClick={rename} />
            </Space>
          ) : (
            <SearchHighLight text={props.data.nodeName} keyword={props.keyword} />
          )}
        </div>
      </div>

      <div className='right'>
        <Dropdown menu={menu} trigger={['click']}>
          <Button
            type='text'
            size='small'
            icon={<MoreOutlined style={{ fontSize: '14px' }} />}
            onClick={(e) => e.stopPropagation()}
          />
        </Dropdown>
      </div>
    </CollectionNodeTitleWrapper>
  );
};

export default CollectionNodeTitle;
