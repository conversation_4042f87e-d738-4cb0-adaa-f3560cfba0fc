import {
  DeleteOutlined,
  DownOutlined,
  ExclamationCircleFilled,
  PlayCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  ArexMenuFC,
  CategoryKey,
  EmptyWrapper,
  getLocalStorage,
  Operator,
  RequestMethodEnum,
  SearchDataType,
  StructuredFilter,
  styled,
  TooltipButton,
  useTranslation,
} from '@arextest/arex-core';
import { useRequest, useSize } from 'ahooks';
import { App, Button, Dropdown, Menu, Tag, Tree } from 'antd';
import type { DataNode, DirectoryTreeProps } from 'antd/lib/tree';
import { cloneDeep } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Icon, WorkspacesMenu } from '@/components';
import { CollectionNodeType, EMAIL_KEY, PanesType } from '@/constant';
import { useNavPane } from '@/hooks';
import CollectionNodeTitle, {
  CollectionNodeTitleProps,
} from '@/menus/Collection/CollectionNodeTitle';
import CollectionsImportExport from '@/menus/Collection/ImportExport';
import { FileSystemService, ReportService } from '@/services';
import { CollectionType } from '@/services/FileSystemService';
import { useCollections, useMenusPanes, useWorkspaces } from '@/store';
import { negate } from '@/utils';
import treeFilter from '@/utils/treeFilter';
// import IconArchive from '~icons/lucide/archive';

const CollectionWrapper = styled.div`
  width: 100%;
  overflow: hidden;

  .collection-content-wrapper {
    padding: 8px 8px;
  }

  .ant-spin-nested-loading,
  .ant-spin {
    height: 100%;
    max-height: 100% !important;
  }

  .collection-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .ant-tree {
    background-color: transparent;
  }

  .ant-tree-node-selected .content {
    color: ${(props) => props.theme.colorText};
  }

  .ant-tree-node-content-wrapper {
    overflow-y: visible; //解决拖拽图标被隐藏
    overflow-x: visible; //允许内容溢出
    white-space: nowrap; //溢出不换行
  }
`;

const CollectionTreeWrapper = styled.div`
  overflow-x: auto;
  overflow-y: hidden;

  /* 自定义滚动条样式，将滚动条放在底部 */
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #88888844;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #888888aa;
  }
`;

const CollectionTree = styled(Tree<CollectionType>)`
  width: 100%; // 限制树形结构宽度

  .ant-tree-node-selected {
    background-color: ${(props) => props.theme.colorPrimaryBgHover} !important;
  }

  /* 确保节点内容不会撑开整个树形结构 */
  .ant-tree-node-content-wrapper {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }
`;

export type CollectionTreeType = CollectionType & DataNode;

const Collection: ArexMenuFC = (props) => {
  const { t } = useTranslation(['components']);
  const { message, modal } = App.useApp();
  const { confirm } = modal;
  const navPane = useNavPane();
  const size = useSize(() => document.getElementById('arex-menu-wrapper'));

  const userName = getLocalStorage<string>(EMAIL_KEY) as string;

  const { activeWorkspaceId, workspaces, getWorkspaces, setActiveWorkspaceId } = useWorkspaces();
  const { activePane, reset: resetPane } = useMenusPanes();
  const { loading, collectionsTreeData, collectionsFlatData, getCollections, getPath } =
    useCollections();

  const [searchValue, setSearchValue] = useState<SearchDataType>();
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [multiSelectedKeys, setMultiSelectedKeys] = useState<string[]>([]);

  // requestId structure: workspaceId-nodeTypeStr-id
  const selectedKeys = useMemo(() => {
    const id = props.value?.split('-')?.[2];
    return id ? [id] : undefined;
  }, [props.value]);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]); // TODO 初始化展开的节点
  const [showModalImportExport, setShowModalImportExport] = useState(false);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  useEffect(() => {
    if (location.pathname.startsWith('/collection')) {
      useWorkspaces.setState({ activeWorkspaceId: activeWorkspaceId || workspaces[0]?.id });
      getCollections();
    }
  }, [location.pathname]);

  // auto expand by active pane id
  useEffect(() => {
    activePane &&
      activePane.type === PanesType.REQUEST &&
      setExpandedKeys((expandedKeys) => Array.from(new Set([...expandedKeys, activePane.id])));
  }, [activePane]);

  // 清除多选状态当路由变化时
  useEffect(() => {
    return () => {
      setMultiSelectedKeys([]);
    };
  }, [location.pathname]);

  // 处理触摸板水平滚动事件
  const handleWrapperWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    // 只处理水平滚动
    if (Math.abs(e.deltaX) > 0) {
      // 手动处理滚动，不需要阻止默认行为
      e.currentTarget.scrollLeft += e.deltaX;
    }
  }, []);





  const { data: labelData = [] } = useRequest(
    () => ReportService.queryLabels({ workspaceId: activeWorkspaceId }),
    {
      ready: !!activeWorkspaceId,
      refreshDeps: [activeWorkspaceId],
    },
  );

  const filterTreeData = useMemo(
    // filter nodeType === 3 是为了隐藏第一层级只显示文件夹类型（由于新增请求时会新增一个临时的request到树形目录的第一层）
    // TODO filter 逻辑待优化
    () =>
      searchValue?.keyword
        ? treeFilter(searchValue.keyword, cloneDeep(collectionsTreeData), 'nodeName')
        : collectionsTreeData,
    [collectionsTreeData, searchValue?.keyword],
  );

  const dataList: { key: string; title: string; labelIds: string | null }[] = useMemo(
    () =>
      Array.from(collectionsFlatData).map(([key, value]) => ({
        key,
        title: value.nodeName,
        labelIds: value.labelIds,
      })),
    [collectionsFlatData],
  );

  const options = useMemo(
    () => [
      {
        category: CategoryKey.Label,
        operator: [Operator.EQ, Operator.NE],
        value: labelData.map((label) => ({
          label: <Tag color={label.color}>{label.labelName}</Tag>,
          key: label.id,
        })),
      },
    ],
    [labelData],
  );

  const workspacesOptions = useMemo(
    () =>
      workspaces.map((workspace) => ({
        value: workspace.id,
        label: workspace.workspaceName,
      })),
    [workspaces],
  );

  const handleSelect: DirectoryTreeProps<CollectionTreeType>['onSelect'] = (keys, info) => {
    // 更新多选状态
    const { nativeEvent } = info;
    const nodeId = info.node.infoId;

    // 处理多选逻辑
    if (nativeEvent.shiftKey) {
      // Shift键按下 - 连续多选
      if (multiSelectedKeys.length > 0) {
        const lastSelectedKey = multiSelectedKeys[multiSelectedKeys.length - 1];
        const allNodes = Array.from(collectionsFlatData.keys());
        const startIndex = allNodes.indexOf(lastSelectedKey);
        const endIndex = allNodes.indexOf(nodeId);

        if (startIndex !== -1 && endIndex !== -1) {
          const start = Math.min(startIndex, endIndex);
          const end = Math.max(startIndex, endIndex);
          const selectedRange = allNodes.slice(start, end + 1);
          setMultiSelectedKeys(selectedRange);
        }
      } else {
        setMultiSelectedKeys([nodeId]);
      }
    } else if (nativeEvent.metaKey || nativeEvent.ctrlKey) {
      // Command/Ctrl键按下 - 非连续多选
      if (multiSelectedKeys.includes(nodeId)) {
        // 如果已经选中，则取消选中
        setMultiSelectedKeys(multiSelectedKeys.filter((key) => key !== nodeId));
      } else {
        // 如果未选中，则添加到选中列表
        setMultiSelectedKeys([...multiSelectedKeys, nodeId]);
      }
    } else {
      // 没有按修饰键 - 单选
      setMultiSelectedKeys([nodeId]);

      // 单选时执行原有的导航逻辑
      if (info.node.nodeType !== CollectionNodeType.folder) {
        const icon =
          info.node.nodeType === CollectionNodeType.interface
            ? info.node.method || undefined
            : info.node.nodeType === CollectionNodeType.case
            ? info.node.caseSourceType === 1
              ? 'arex'
              : 'case'
            : undefined;

        navPane({
          type: PanesType.REQUEST,
          id: `${activeWorkspaceId}-${info.node.nodeType}-${info.node.infoId}`,
          name: info.node.nodeName,
          icon,
        });
      }
    }
  };

  const handleChange = (value: SearchDataType) => {
    const { keyword, structuredValue = [] } = value;
    let newExpandedKeys;
    if (!structuredValue?.length && !keyword) {
      // newExpandedKeys = dataList.map((item) => item.title);
    } else {
      newExpandedKeys = dataList
        .map((item) => {
          const lowerCaseKeyword = keyword?.toLowerCase() || '';
          const keywordFiltered =
            !keyword ||
            (lowerCaseKeyword &&
              (item.title.toLowerCase().includes(lowerCaseKeyword) ||
                item.key.toLowerCase().includes(lowerCaseKeyword)));
          let structuredFiltered = true;

          for (let i = 0; i < structuredValue.length; i++) {
            const structured = structuredValue[i];

            if (structured.category === CategoryKey.Label) {
              // search for labelIds
              const include = negate(
                item.labelIds?.includes(structured.value as string),
                structured.operator === Operator.NE,
              );

              if (!include) {
                structuredFiltered = false;
                break;
              }
            }
          }

          return keywordFiltered && structuredFiltered
            ? collectionsFlatData.get(item.key)?.pid
            : null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      setExpandedKeys(newExpandedKeys as string[]);
    }
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const { run: createCollection } = useRequest(
    () =>
      FileSystemService.addCollectionItem({
        id: activeWorkspaceId,
        userName,
      }),
    {
      manual: true,
      onSuccess() {
        getCollections();
      },
    },
  );

  const { run: remove } = useRequest(FileSystemService.moveCollectionItem, {
    manual: true,
    onSuccess(success) {
      success && getCollections();
    },
  });

  // 删除多个选中的节点
  const { run: multiDelete } = useRequest(
    (nodeIds: string[]) => {
      // 获取每个节点的完整路径
      const removeNodePaths = nodeIds.map((id) => {
        const node = collectionsFlatData.get(id);
        if (node) {
          return getPath(id).map((path) => path.id);
        }
        return [id]; // 如果找不到节点，则使用节点ID作为路径
      });

      return FileSystemService.removeItems({
        id: activeWorkspaceId,
        removeNodePaths,
        userName,
      });
    },
    {
      manual: true,
      onSuccess: () => {
        // 关闭与被删除节点相关的标签页
        useMenusPanes.setState((state) => {
          // 过滤掉与被删除节点相关的标签页
          const filteredPanes = state.panes.filter((pane) => {
            // 检查标签页ID是否包含被删除的节点ID
            return !multiSelectedKeys.some((nodeId) => {
              // 标签页ID格式通常为：workspaceId-nodeType-nodeId
              const paneNodeId = pane.id.split('-')[2];
              return paneNodeId === nodeId;
            });
          });

          // 如果当前活动的标签页被删除了，需要激活其他标签页
          if (
            state.activePane &&
            multiSelectedKeys.some((nodeId) => {
              const activeNodeId = state.activePane?.id.split('-')[2];
              return activeNodeId === nodeId;
            })
          ) {
            // 如果还有其他标签页，激活第一个
            if (filteredPanes.length > 0) {
              state.setActivePane(filteredPanes[0].id);
            } else {
              state.setActivePane(undefined);
            }
          }

          return { panes: filteredPanes };
        });

        getCollections();
        setMultiSelectedKeys([]);
        setContextMenuVisible(false);
      },
    },
  );

  // 处理右键点击事件
  const handleRightClick = (info: any) => {
    // 如果点击的节点不在已选中的节点中，则先选中该节点
    if (!multiSelectedKeys.includes(info.node.infoId)) {
      setMultiSelectedKeys([info.node.infoId]);
    }

    // 设置右键菜单位置
    setContextMenuPosition({
      x: info.event.clientX,
      y: info.event.clientY,
    });

    // 显示右键菜单
    setContextMenuVisible(true);
  };

  // 处理删除选中节点
  const handleDeleteSelected = () => {
    if (multiSelectedKeys.length > 0) {
      confirm({
        title: t('你确定吗？'),
        content: `${t('确定要删除选中的', { ns: 'components' })} ${multiSelectedKeys.length} ${t(
          '个项目吗？',
          { ns: 'components' },
        )}`,
        icon: <ExclamationCircleFilled />,
        okText: t('确定'),
        okType: 'danger',
        cancelText: t('取消'),
        onOk: () => multiDelete(multiSelectedKeys),
      });
    }
  };

  // 隐藏右键菜单
  const hideContextMenu = () => {
    setContextMenuVisible(false);
  };

  // 添加点击页面其他区域时隐藏右键菜单的功能
  useEffect(() => {
    const handleClickOutside = () => {
      setContextMenuVisible(false);
    };

    if (contextMenuVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [contextMenuVisible]);

  const onDrop = useCallback<NonNullable<DirectoryTreeProps<CollectionTreeType>['onDrop']>>(
    (value) => {
      /**
       * 节点拖动规则:
       * 1. CollectionNodeType.folder 只能直属于 CollectionNodeType.folder
       * 2. CollectionNodeType.request 只能直属于 CollectionNodeType.folder
       * 3. CollectionNodeType.case 只能直属于 CollectionNodeType.request
       */
      const dragNodeType = value.dragNode.nodeType;
      let parentNodeType = CollectionNodeType.folder;

      const dragPos = value.dragNode.pos
        .split('-')
        .slice(1) // remove root node
        .map((p) => Number(p));
      const nodePos = value.node.pos
        .split('-')
        .slice(1) // remove root node
        .map((p) => Number(p));

      let dragTree = cloneDeep(collectionsTreeData);
      let nodeTree = cloneDeep(collectionsTreeData);
      const fromNodePath: string[] = [];
      const toParentPath: string[] = [];
      let toIndex = value.dropPosition;

      dragPos.forEach((p) => {
        fromNodePath.push(dragTree[p].infoId);
        dragTree = dragTree[p].children;
      });

      nodePos.forEach((p) => {
        toParentPath.push(nodeTree[p].infoId);
        parentNodeType = nodeTree[p].nodeType;
        nodeTree = nodeTree[p].children;
      });

      // console.log(value);
      // console.log(dragNodeType, parentNodeType);
      // 校验拖拽节点是否合规: 3,3 || 1,3 || 2,1

      if (
        (dragNodeType === CollectionNodeType.folder &&
          parentNodeType !== CollectionNodeType.folder) ||
        (dragNodeType === CollectionNodeType.interface &&
          // @ts-ignore
          parentNodeType === CollectionNodeType.case) ||
        (dragNodeType === CollectionNodeType.case &&
          (parentNodeType as CollectionNodeType) === CollectionNodeType.folder)
      )
        return console.error('拖拽节点不合规');

      // 同类型拖拽，层级自动提升
      if (dragNodeType === parentNodeType) {
        // 文件夹拖动的情况
        if (dragNodeType === CollectionNodeType.folder) {
          if (dragPos.length === nodePos.length && value.dropToGap) {
            // console.log('文件夹平级的情况');
            toParentPath.pop();
          } else {
            // console.log('文件夹嵌套拖进的情况');
            value.dropToGap && toParentPath.pop();
          }
        } else {
          toIndex++;
          toParentPath.pop();
        }
      } else toIndex = 0;

      remove({
        id: activeWorkspaceId,
        fromNodePath,
        toParentPath,
        toIndex: toIndex < 0 ? 0 : toIndex,
      });
    },
    [activeWorkspaceId, collectionsTreeData, remove],
  );

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys as string[]);
    setAutoExpandParent(false);
  };

  const handleAddNode: CollectionNodeTitleProps['onAddNode'] = (infoId, nodeType) => {
    handleSelect([infoId], {
      // @ts-ignore
      node: {
        infoId,
        nodeType,
        method: RequestMethodEnum.GET,
      },
    });
    setExpandedKeys([...expandedKeys, infoId]);
  };

  const { run: createWorkspace } = useRequest(FileSystemService.createWorkspace, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        message.success(t('workSpace.createSuccess'));
        resetPane();
        getWorkspaces(res.workspaceId);
      }
    },
  });

  const handleAddWorkspace = (workspaceName: string) => {
    createWorkspace({ userName, workspaceName });
  };

  const handleEditWorkspace = (workspaceId: string) => {
    navPane({
      type: PanesType.WORKSPACE,
      id: workspaceId,
    });
  };

  return (
    <CollectionWrapper id='collectTree'>
      <WorkspacesMenu
        value={activeWorkspaceId}
        options={workspacesOptions}
        onChange={setActiveWorkspaceId}
        onAdd={handleAddWorkspace}
        onEdit={handleEditWorkspace}
      />

      <div className='collection-content-wrapper'>
        <EmptyWrapper
          empty={!loading && !collectionsTreeData.length}
          loading={loading}
          description={
            <Button type='primary' onClick={createCollection}>
              {t('collection.create_new')}
            </Button>
          }
        >
          {/* Tree模块上方的几个按钮 */}
          <StructuredFilter
            size='small'
            className={'collection-header-search'}
            showSearchButton={false}
            prefix={
              <div
                style={{
                  height: '27px',
                  marginRight: '8px',
                  display: 'flex',
                  flexWrap: 'wrap',
                  alignContent: 'center',
                }}
              >
                <TooltipButton
                  icon={<PlusOutlined />}
                  type='text'
                  size='small'
                  title={t('collection.create_new')}
                  onClick={createCollection}
                />

                <TooltipButton
                  id='arex-import-btn'
                  icon={<Icon name='ArchiveRestore' />}
                  title={t('collection.import_export')}
                  onClick={() => {
                    setShowModalImportExport(true);
                  }}
                />

                <TooltipButton
                  icon={<PlayCircleOutlined />}
                  title={t('collection.batch_run')}
                  onClick={() => {
                    navPane({
                      type: PanesType.BATCH_RUN,
                      id: activeWorkspaceId,
                    });
                  }}
                />
              </div>
            }
            labelDataSource={labelData.map((item) => ({
              id: item.id,
              name: item.labelName,
              color: item.color,
            }))}
            options={options}
            placeholder={'Search for Name or Id'}
            onChange={handleChange}
          />
          <CollectionTreeWrapper onWheel={handleWrapperWheel}>
            <CollectionTree
              showLine
              blockNode
              multiple
              height={size?.height && size.height - 100}
              selectedKeys={multiSelectedKeys.length > 0 ? multiSelectedKeys : selectedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              switcherIcon={<DownOutlined />}
              treeData={filterTreeData}
              fieldNames={{ title: 'nodeName', key: 'infoId', children: 'children' }}
              onDrop={onDrop}
              onExpand={onExpand}
              onSelect={handleSelect}
              onRightClick={handleRightClick}
              draggable={{ icon: false }}
              titleRender={(data) => (
                <CollectionNodeTitle
                  data={data}
                  keyword={searchValue?.keyword}
                  onAddNode={handleAddNode}
                />
              )}
            />
          </CollectionTreeWrapper>

          {/* 右键菜单 */}
          {contextMenuVisible && (
            <div
              style={{
                position: 'fixed',
                left: contextMenuPosition.x,
                top: contextMenuPosition.y,
                zIndex: 1000,
              }}
            >
              <Dropdown
                open={contextMenuVisible}
                onOpenChange={hideContextMenu}
                trigger={[]}
                menu={{
                  items: [
                    {
                      key: 'delete',
                      icon: <DeleteOutlined />,
                      label: t('collection.delete_selected', {
                        ns: 'components',
                        defaultValue: `Delete Selected (${multiSelectedKeys.length})`,
                      }),
                      danger: true,
                      onClick: handleDeleteSelected,
                    },
                  ],
                }}
              >
                <div style={{ width: 0, height: 0 }}></div>
              </Dropdown>
            </div>
          )}
          {/*弹框*/}
          <CollectionsImportExport
            show={showModalImportExport}
            onHideModal={() => {
              setShowModalImportExport(false);
            }}
          />
        </EmptyWrapper>
      </div>
    </CollectionWrapper>
  );
};

export default Collection;
