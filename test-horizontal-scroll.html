<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Horizontal Scroll</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-container {
            width: 300px;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 20px 0;
        }
        
        .ant-tree-node-content-wrapper {
            width: 100%;
            overflow-y: visible;
            overflow-x: auto;
            white-space: nowrap;
            border: 1px solid #eee;
            padding: 8px;
            margin: 5px 0;
            
            /* 自定义滚动条样式，将滚动条放在底部 */
        }
        
        .ant-tree-node-content-wrapper::-webkit-scrollbar {
            height: 6px;
            width: 6px;
        }
        
        .ant-tree-node-content-wrapper::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .ant-tree-node-content-wrapper::-webkit-scrollbar-thumb {
            background: #88888844;
            border-radius: 3px;
        }
        
        .ant-tree-node-content-wrapper::-webkit-scrollbar-thumb:hover {
            background: #888888aa;
        }
        
        .long-text {
            display: inline-block;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>CollectionTree 水平滚动测试</h1>
    
    <h2>修改前的效果（模拟）：</h2>
    <div class="test-container">
        <div style="width: 10%; overflow-x: hidden; text-overflow: ellipsis; white-space: nowrap; border: 1px solid #eee; padding: 8px;">
            <span class="long-text">这是一个非常长的集合名称，应该被截断并显示省略号</span>
        </div>
    </div>
    
    <h2>修改后的效果：</h2>
    <div class="test-container">
        <div class="ant-tree-node-content-wrapper">
            <span class="long-text">这是一个非常长的集合名称，现在可以通过水平滚动来查看完整内容</span>
        </div>
        <div class="ant-tree-node-content-wrapper">
            <span class="long-text">Another very long collection name that should be scrollable horizontally</span>
        </div>
        <div class="ant-tree-node-content-wrapper">
            <span class="long-text">第三个超级长的集合名称用来测试水平滚动功能是否正常工作，滚动条应该出现在底部</span>
        </div>
    </div>
    
    <h2>说明：</h2>
    <ul>
        <li>修改前：长文本被截断，显示省略号，无法看到完整内容</li>
        <li>修改后：长文本可以通过水平滚动查看，滚动条显示在底部</li>
        <li>滚动条样式与项目中的全局样式保持一致</li>
    </ul>
</body>
</html>
