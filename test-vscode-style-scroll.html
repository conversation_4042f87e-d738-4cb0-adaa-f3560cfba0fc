<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Horizontal Scroll - VS Code Style</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-container {
            width: 300px;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 20px 0;
        }
        
        /* 模拟修改前的效果 */
        .old-style {
            width: 100%;
            overflow: hidden;
        }
        
        .old-style .tree-node {
            width: 10%;
            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            border: 1px solid #eee;
            padding: 8px;
            margin: 5px 0;
        }
        
        /* 模拟修改后的效果 - VS Code 资源管理器风格 */
        .new-style {
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #eee;
            padding: 8px;
        }
        
        .new-style::-webkit-scrollbar {
            height: 6px;
            width: 6px;
        }
        
        .new-style::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .new-style::-webkit-scrollbar-thumb {
            background: #88888844;
            border-radius: 3px;
        }
        
        .new-style::-webkit-scrollbar-thumb:hover {
            background: #888888aa;
        }
        
        .tree-content {
            min-width: max-content;
        }
        
        .tree-node {
            white-space: nowrap;
            padding: 4px 0;
            margin: 2px 0;
        }
        
        .long-text {
            display: inline-block;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .folder-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #ffd700;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .file-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #87ceeb;
            margin-right: 8px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>CollectionTree 水平滚动测试 - VS Code 资源管理器风格</h1>
    
    <h2>修改前的效果（每个节点被截断）：</h2>
    <div class="test-container">
        <div class="old-style">
            <div class="tree-node">
                <span class="folder-icon"></span>
                <span class="long-text">这是一个非常长的集合名称，应该被截断并显示省略号</span>
            </div>
            <div class="tree-node">
                <span class="file-icon"></span>
                <span class="long-text">Another very long collection name that gets truncated</span>
            </div>
        </div>
    </div>
    
    <h2>修改后的效果（整个容器可以水平滚动，类似 VS Code）：</h2>
    <div class="test-container">
        <div class="new-style">
            <div class="tree-content">
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">这是一个非常长的集合名称，现在可以通过整个容器的水平滚动来查看完整内容</span>
                </div>
                <div class="tree-node">
                    <span class="file-icon"></span>
                    <span class="long-text">Another very long collection name that should be scrollable horizontally at container level</span>
                </div>
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">第三个超级长的集合名称用来测试水平滚动功能是否正常工作，滚动条应该出现在容器底部</span>
                </div>
                <div class="tree-node">
                    <span class="file-icon"></span>
                    <span class="long-text">GET /api/very/long/endpoint/path/that/extends/beyond/container/width</span>
                </div>
            </div>
        </div>
    </div>
    
    <h2>说明：</h2>
    <ul>
        <li><strong>修改前：</strong>每个节点内容被截断，显示省略号，无法看到完整内容</li>
        <li><strong>修改后：</strong>整个树形容器可以水平滚动，类似 VS Code 资源管理器</li>
        <li><strong>滚动条位置：</strong>滚动条显示在整个容器的底部，而不是每个节点</li>
        <li><strong>用户体验：</strong>更符合用户在 VS Code 中的使用习惯</li>
    </ul>
</body>
</html>
