<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Touchpad Horizontal Scroll</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .test-container {
            width: 400px;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 20px 0;
        }
        
        /* 模拟修改前的效果 */
        .old-style {
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #eee;
            padding: 8px;
            height: 200px;
        }
        
        .old-style::-webkit-scrollbar {
            height: 6px;
        }
        
        .old-style::-webkit-scrollbar-thumb {
            background: #88888844;
            border-radius: 3px;
        }
        
        /* 模拟修改后的效果 - 支持触摸板滚动 */
        .new-style {
            overflow-x: auto;
            overflow-y: hidden;
            border: 1px solid #eee;
            padding: 8px;
            height: 200px;
        }
        
        .new-style::-webkit-scrollbar {
            height: 6px;
        }
        
        .new-style::-webkit-scrollbar-thumb {
            background: #88888844;
            border-radius: 3px;
        }
        
        .new-style * {
            pointer-events: auto;
        }
        
        .tree-content {
            min-width: max-content;
        }
        
        .tree-node {
            white-space: nowrap;
            padding: 8px 0;
            margin: 2px 0;
            cursor: pointer;
            border-radius: 4px;
            padding-left: 16px;
        }
        
        .tree-node:hover {
            background-color: #f0f0f0;
        }
        
        .long-text {
            display: inline-block;
            background: #e6f7ff;
            padding: 4px 8px;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .folder-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #ffd700;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .file-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #87ceeb;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>触摸板水平滚动测试</h1>
    
    <div class="status" id="scrollStatus">
        滚动状态：等待滚动...
    </div>
    
    <h2>修改前（只能在滚动条上滚动）：</h2>
    <div class="test-container">
        <div class="old-style" id="oldContainer">
            <div class="tree-content">
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">这是一个非常长的集合名称，需要水平滚动才能看到完整内容，但是只能在滚动条上滚动</span>
                </div>
                <div class="tree-node">
                    <span class="file-icon"></span>
                    <span class="long-text">GET /api/very/long/endpoint/path/that/extends/beyond/container/width/and/requires/scrolling</span>
                </div>
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">另一个超级长的文件夹名称，包含很多字符，需要滚动查看</span>
                </div>
            </div>
        </div>
    </div>
    
    <h2>修改后（在任何位置都能用触摸板滚动）：</h2>
    <div class="test-container">
        <div class="new-style" id="newContainer">
            <div class="tree-content">
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">这是一个非常长的集合名称，现在可以在任何位置用触摸板水平滚动查看完整内容</span>
                </div>
                <div class="tree-node">
                    <span class="file-icon"></span>
                    <span class="long-text">POST /api/very/long/endpoint/path/that/extends/beyond/container/width/and/supports/touchpad/scrolling</span>
                </div>
                <div class="tree-node">
                    <span class="folder-icon"></span>
                    <span class="long-text">另一个超级长的文件夹名称，包含很多字符，支持触摸板滚动</span>
                </div>
                <div class="tree-node">
                    <span class="file-icon"></span>
                    <span class="long-text">PUT /api/another/extremely/long/endpoint/path/for/testing/horizontal/scroll/functionality</span>
                </div>
            </div>
        </div>
    </div>
    
    <h2>测试说明：</h2>
    <ul>
        <li><strong>修改前：</strong>只有当鼠标在滚动条上时，才能用触摸板左右滑动</li>
        <li><strong>修改后：</strong>鼠标在任何树节点上时，都可以用触摸板左右滑动</li>
        <li><strong>测试方法：</strong>将鼠标放在树节点上，然后用触摸板左右滑动</li>
    </ul>

    <script>
        // 为修改后的容器添加滚动事件处理
        const newContainer = document.getElementById('newContainer');
        const statusDiv = document.getElementById('scrollStatus');
        
        function handleWheel(e) {
            if (Math.abs(e.deltaX) > 0) {
                statusDiv.textContent = `水平滚动检测到: deltaX=${e.deltaX.toFixed(2)}, 滚动位置: ${newContainer.scrollLeft}px`;
                
                // 手动处理滚动
                e.preventDefault();
                newContainer.scrollLeft += e.deltaX;
            }
        }
        
        // 添加滚动事件监听
        newContainer.addEventListener('wheel', handleWheel, { passive: false });
        
        // 监听滚动位置变化
        newContainer.addEventListener('scroll', () => {
            statusDiv.textContent = `当前滚动位置: ${newContainer.scrollLeft}px`;
        });
    </script>
</body>
</html>
